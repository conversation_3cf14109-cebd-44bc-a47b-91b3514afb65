### 商业化-Q3 工作排期表

| 阶段 | 任务模块 | 具体工作内容 | 输出成果 | 完成时间节点 | 优先级 | 并行逻辑 / 依赖 | 备注 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **子阶段 1:**<br>**核心方案商贾**<br>**(7.8-7.18)** | 竞品功能深度拆解 | 1. 根据《XPrinter&芯烨管家-商业化计划》中的市场调研与竞品分析等章节，深度拆解核心指标<br>2. 输出竞品差异对比（xx竞品） | 竞品功能对比表（含核心指标） | 7.12 | 高 | | |
| | 用户需求整合 | 1. 分析逻辑、识别、批量打印等场景痛点<br>2. 映射「功能 - 场景 - 付费意愿」关系 | 用户需求映射表 | 7.12 | 高 | 依赖“竞品分析”完成 | |
| | 原型初稿设计 | 1. 输出数据恢复、打印日志、识图新建、扫码新建等 **原型** | 原型初稿（带交互） | 7.18 | 高 | 依赖“用户需求”完成 | |
| | 技术方案核心输出 | 0. VIP体系设计（存储分层、付费功能等）<br>1. 数据恢复，确定 **7天（免费）/30天（VIP）** 存储架构<br>2. 识图新建，选定一家第三方服务商（百度、合合等），设计识别流程<br>3. 文本引用/批量打印/共享打印，明确 PC 接口复用核心逻辑、PC交互迁移到APP的可行方案 | 技术方案核心文档（V1.0） | 7.18 | 高 | 依赖“用户需求”完成 | |
| | 代理商平台搭建（移科） | 1. 代理商渠道推广平台，通过“邀请码”关联<br>2. 设计见《XPrinter推广方案&代理商推广平台》<br>3. 远期规则设计（营销团队） | 代理商平台<br>1. 可生成 Xprinter app的代理商“邀请码”<br>2. 核心接口端点（注册未打印、注册已打印、购买VIP） | 7.18 | 高 | | 7月16号可以联调；<br>第3点远期规则的对接需要同频移科 |
| | 用户付费流程梳理 | 1. 设计从「功能触发付费->选择套餐->支付完成->权益生效」的全流程逻辑<br>2. 明确付费入口位置（如功能页弹窗、会员中心独立入口）<br>3. 规划支付方式（微信 / 支付宝 / 银联）及优惠策略 | 付费流程设计文档 + 流程图 | / | 中 | 依赖“用户需求整合”完成 | |
| | APP付费排程梳理 | 1. 明确“付费墙”的功能<br>2. 承接用户付费，“付费墙”功能清单 | 付费墙功能设计（思维图） | / | 中 | 依赖“用户需求整合”完成 | |
| | APP对接代理商体系 | 1. APP端“邀请码”的开发<br>2. 关键节点预留接入“代理商平台” | XPrinter APP-发版（android扫码, ios TestFlight） | 7.22 | 高 | 依赖“代理商平台搭建”完成 | |
| | 开发-基础功能架构设计 | 1. 搭建 VIP 用户体系<br>2. 模板分层设计（免费 200/VIP 不限） | 基础功能框架（VIP用户体系） | 7.22 | 高 | 依赖“技术方案 V1.0” | |
| **子阶段 2:**<br>**需求细化/开发启动**<br>**(7.18-8.8)** | 原型初稿设计 | 0. 数据恢复、打印日志、识图新建原型定稿<br>1. 输出文本引用、**批量打印、共享打印**初稿原型<br>2. VIP 权益设计 | 原型（带交互） | 7.31 | 高 | | |
| | UI/UX 设计 | 1. 数据恢复、识图新建、扫码新建 UI/UX设计<br>2. 输出最终付费引导设计 | UI 高保真原型稿 | 7.31 | 中 | 与“开发-核心新功能”并行 | |
| | 开发-核心新功能 | 1. 数据恢复：搭建日志系统，实现**7天恢复接口**（VIP 接口预留）<br>2. 识图新建：对接 OCR，完成图片上传 -> 识别流程 -> 模板创建<br>3. 扫码新建：基于商品信息库和默认模板，完成模板创建 | 新功能核心框架（数据 + 识图） | 8.8 | 高 | 依赖“技术方案 V1.0” | |
| | 开发-新功能深化 | 1. 数据恢复：开通**30天 VIP 恢复接口**，测试跨设备恢复<br>2. 识图新建：优化准确率（>=90%） | 新功能完整包 | 8.31 | 高 | - | |
| | UI/UX 设计 | 1. 文本引用、批量打印、共享打印 UI/UX设计 | UI 高保真原型终稿 | 8.22 | 高 | 与“开发-PC迁移功能”并行 | |
| **子阶段 3:**<br>**核心功能深化**<br>**(8.8-8.31)** | 开发-PC迁移功能 | 1. 文本引用：复用 PC 逻辑，测试“文本变更->二维码同步”，优化交互<br>2. 批量打印：复用 PC 逻辑，完善队列管理（进度 + 异常）<br>3. 共享打印：复用 PC 逻辑（**场景有点问题，确认是否要迁移**） | 适配功能完整包 | 8.31 | 高 | - | |
| | 开发-后台管理系统 | 1. 功能的显示隐藏（灰度）<br>2. VIP权益配置（付费功能；存储分层） | / | 8.31 | 中 | 依赖“VIP 体系设计”完成 | |
| | 测试-准备测试用例 | 1. 全功能测试用例 | 《测试用例》 | 8.31 | 高 | - | |
| **子阶段 4:**<br>**测试与验收**<br>**(9.1-9.30)** | 全功能测试 | 1. 新功能：数据恢复（7天 / 30天）、识图新建（多场景）<br>2. PC迁移功能：文本引用（同步性）、批量打印（流程）、共享打印<br>3. 兼容主流机型（Android 8+/iOS 13+） | 测试报告（通过率>=95%?） | 9.15 | 高 | - | |
| | 用户体验打磨 | 1. 邀请50名核心用户体验，改善用户体验<br>2. 简化升级VIP流程 | 用户体验优化方案 | 9.21 | 高 | 依赖“全功能测试”结果 | |
| | 亮点功能持续引入 | 1. **AI智能排版**, 根据用户模板进行学习，给用户提效 | AI智能排版，亮点功能引爆用户 | 9.21 | 中 | | |
| | 合规与安全验收 | 1. 隐私政策覆盖数据恢复等新功能<br>2. 检测文本引用二维码防篡改<br>3. 其他安全合规验收 | 合规报告 | 9.25 | 高 | - | |
| | 上线准备 | 1. 编写全功能说明书<br>2. 配置管理后台、关联 VIP 权益（恢复时长、识图精度） | 上线 | 9.30 | 高 | 依赖“合规 + 体验”完成 | | 