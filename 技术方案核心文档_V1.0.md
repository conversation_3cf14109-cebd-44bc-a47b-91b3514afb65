# XPrinter商业化技术方案核心文档 V1.0

## 文档信息

- **版本**: V1.0
- **创建日期**: 2024年7月16日
- **负责人**: 王鹏飞
- **完成时间节点**: 2024年7月18日

---

## 1. VIP体系设计

### 1.1 用户分层架构

```
免费用户 (Free)
├── 云端存储: 100个模板
├── 数据恢复: 无
├── 基础功能: 标准打印、基础模板、扫码新建、文本引用、批量打印、打印日志
└── 广告展示: 开屏广告

VIP用户 (Premium)
├── 云端存储: 无限制
├── 数据恢复: 6个月
├── 高级功能: 数据恢复、价签打印、识图新建、团队协作、存储扩容
└── 广告展示: 无
```

### 1.2 存储分层设计

#### 数据存储策略

- **免费用户**:

  - 打印记录保存最近100条
  - 云端存储模板限制: 100个
- **VIP用户**:

  - 打印记录保存最近100条
  - 云端存储模板限制: 不限制

#### 技术实现

```
数据库设计:
- user_data_retention (用户数据保留策略表)
- print_logs (打印日志表，带过期时间字段)
- storage_quota (存储配额管理表)
```

### 1.3 付费功能权限控制

```javascript
// 功能权限映射
const FEATURE_PERMISSIONS = {
  // 基础功能 - 免费用户可用
  'print_logs': ['free', 'vip'],
  'scan_create': ['free', 'vip'],
  'text_reference': ['free', 'vip'],
  'batch_print': ['free', 'vip'],
  'basic_templates': ['free', 'vip'],

  // 高级功能 - VIP用户专享
  'data_recovery_6m': ['vip'],
  'ocr_template_create': ['vip'],
  'price_tag_print': ['vip'],
  'team_collaboration': ['vip'],
  'storage_expansion': ['vip'],
  'unlimited_templates': ['vip'],
  'cloud_sync_priority': ['vip']
}
```

---

## 2. 数据恢复技术方案

### 2.0 数据恢复模块

#### 云端数据功能

- **云端图片存储**: 支持用户上传图片到云端，图片打印功能可选择云端图片
- **云端文件存储**: 支持用户上传文件到云端，文件打印功能可选择云端文件
- **云端模板同步**: 用户新增的标签模板默认保存到云端，支持多设备同步

#### 数据恢复入口设计

- **入口1 - 个人模板**: 个人模板页面工具栏新增"数据恢复"入口
- **入口2 - 芯烨云服务**: 应用-芯烨云服务-数据恢复模块

#### 可恢复数据类型

- 标签模板
- 云端图片文件
- 云端文档文件

### 2.1 存储架构设计

#### 分层存储策略

```
Level 1: 本地存储 (SQLite)
├── 最近3天数据
├── 快速访问
└── 离线可用

Level 2: 云端热存储 (Redis/MySQL)
├── 免费用户: 无数据恢复功能
├── VIP用户: 6个月内数据恢复
└── 实时同步

Level 3: 云端冷存储 (对象存储OSS)
├── 历史数据归档 (6个月以上)
├── 成本优化
└── 按需恢复
```

### 2.2 接口设计

```javascript

// 数据恢复接口
POST /api/v1/data/recovery
{
  "user_id": "string",
  "data_type": "template|image|file",
  "date_range": {
    "start": "2024-01-01",
    "end": "2024-07-18"
  }
}

// 响应
{
  "code": 200,
  "data": {
    "deleted_items": [
      {
        "id": "template_001",
        "name": "商品标签模板",
        "type": "template",
        "created_time": "2024-06-12 14:30:00",  //恢复后需要保持原来的排序（创建时间）
        "deleted_time": "2024-06-15 14:30:00",
        "original_position": "personal_templates",
        "thumbnail": "https://...",
        "can_recover": true
      }
    ],
    "recovery_count": 25,
    "total_count": 150
  }
}

// 恢复操作接口
POST /api/v1/data/recovery/restore
{
  "user_id": "string",
  "item_ids": ["template_001", "image_002"],
  "restore_to_original": true
}
```

### 2.3 技术实现要点

- **数据加密**: AES-256加密存储
- **压缩算法**: gzip压缩减少存储空间
- **容灾备份**: 多地域备份策略
- **性能优化**: 分页加载 + 懒加载

---

## 3. 打印日志功能方案

### 3.1 功能概述

打印日志功能为免费功能，默认开启，记录用户最近100条打印历史，支持快速查询和重新打印。

### 3.2 数据存储设计

#### 数据结构

```javascript
// 打印记录表结构
const PrintLogSchema = {
  id: 'string',           // 记录ID
  user_id: 'string',      // 用户ID
  template_id: 'string',  // 模板ID（如果是模板打印）
  print_name: 'string',   // 打印任务名称
  print_type: 'template|image|file|barcode', // 打印类型
  printer_info: {
    name: 'string',       // 打印机名称
    model: 'string',      // 打印机型号
    connection: 'bluetooth|wifi|usb' // 连接方式
  },
  print_settings: {
    size: 'string',       // 打印尺寸
    copies: 'number',     // 打印份数
    quality: 'string'     // 打印质量
  },
  content_thumbnail: 'string', // 内容缩略图URL
  print_time: 'datetime',      // 打印时间
  status: 'success|failed|pending', // 打印状态
  created_at: 'datetime',
  updated_at: 'datetime'
}
```

#### 存储策略

- **本地存储**: SQLite存储最近30条记录，支持离线查看
- **云端存储**: MySQL存储最近100条记录，支持多设备同步
- **自动清理**: 超过100条时自动删除最早记录

### 3.3 界面设计

#### 主界面展示

- 按时间倒序展示打印记录
- 显示内容：名称、打印端、尺寸、份数、时间、缩略图
- 支持下拉刷新和上拉加载更多

#### 操作功能

- **查看详情**: 点击记录查看完整打印信息
- **重新编辑**: 进入编辑页面，基于历史内容编辑
- **快捷重打**: 一键重新打印，保持原有设置
- **删除记录**: 支持单条或批量删除

### 3.4 接口设计

```javascript
// 获取打印日志
GET /api/v1/print/logs?page=1&limit=20&type=all

// 重新打印
POST /api/v1/print/reprint
{
  "log_id": "string",
  "copies": 1,
  "printer_id": "string"
}

// 删除打印记录
DELETE /api/v1/print/logs
{
  "log_ids": ["id1", "id2"]
}
```

---

## 4. 扫码新建功能方案

### 4.1 功能概述

扫码新建为免费功能，支持扫描一维码/二维码快速创建模板，自动查询商品信息并生成对应的打印模板。

### 4.2 技术实现流程

```mermaid
graph TD
    A[用户点击扫一扫] --> B[拉起相机]
    B --> C[扫描一维码/二维码]
    C --> D[获取条码数据]
    D --> E{查询商品信息}
    E -->|有商品信息| F[获取商品详情]
    E -->|无商品信息| G[按条码类型新建]
    F --> H[渲染商品信息模板]
    G --> I[渲染条码模板]
    H --> J[用户编辑确认]
    I --> J
    J --> K[保存为模板]
```

### 4.3 商品信息查询

#### 数据源设计

- **本地商品库**: 常用商品信息本地缓存
- **第三方API**: 对接商品信息查询接口
- **用户自定义**: 支持用户添加自定义商品信息

#### 查询逻辑

```javascript
async function queryProductInfo(barcode) {
  // 1. 本地缓存查询
  let product = await localDB.getProduct(barcode);
  if (product) return product;

  // 2. 第三方API查询
  try {
    product = await productAPI.query(barcode);
    if (product) {
      await localDB.cacheProduct(barcode, product);
      return product;
    }
  } catch (error) {
    console.log('API查询失败:', error);
  }

  // 3. 返回空，按条码类型处理
  return null;
}
```

### 4.4 模板自动生成

#### 有商品信息时

```javascript
const productTemplate = {
  elements: [
    {
      type: 'text',
      content: product.name,
      position: { x: 10, y: 10 },
      style: { fontSize: 16, fontWeight: 'bold' }
    },
    {
      type: 'text',
      content: `¥${product.price}`,
      position: { x: 10, y: 35 },
      style: { fontSize: 14, color: 'red' }
    },
    {
      type: 'barcode',
      content: barcode,
      barcodeType: detectBarcodeType(barcode),
      position: { x: 10, y: 60 }
    }
  ]
}
```

#### 无商品信息时

```javascript
const barcodeTemplate = {
  elements: [
    {
      type: detectBarcodeType(barcode), // 'barcode' 或 'qrcode'
      content: barcode,
      position: { x: 10, y: 10 }
    },
    {
      type: 'text',
      content: barcode,
      position: { x: 10, y: 50 },
      style: { fontSize: 12 }
    }
  ]
}
```

### 4.5 接口设计

```javascript
// 扫码识别接口
POST /api/v1/scan/recognize
{
  "image_data": "base64_string",
  "scan_type": "auto|barcode|qrcode"
}

// 商品信息查询接口
GET /api/v1/product/info/{barcode}

// 模板生成接口
POST /api/v1/template/generate-from-scan
{
  "barcode": "string",
  "barcode_type": "ean13|qrcode|...",
  "product_info": {...} // 可选
}
```

---

## 5. 识图新建技术方案

### 5.1 第三方服务商选型对比

| 服务商   | 识别准确率 | 价格(元/次) | 响应时间 | 支持格式    | 支持功能                   | 推荐指数   |
| -------- | ---------- | ----------- | -------- | ----------- | -------------------------- | ---------- |
| 百度OCR  | 95%+       | 0.032       | <2s      | jpg,png,pdf | 文字识别                   | ⭐⭐⭐⭐   |
| 合合信息 | 95%+       | 0.045       | <3s      | jpg,png,pdf | 文字、表格、一维码、二维码 | ⭐⭐⭐⭐⭐ |
| 腾讯OCR  | 94%+       | 0.05        | <2.5s    | jpg,png,pdf | 文字、表格识别             | ⭐⭐⭐⭐   |

**推荐方案**: 合合信息 (功能最全面，支持文字、表格、条码识别)

#### 合合信息优势分析

- **全能扫描王技术**: 基于合合信息自研的OCR引擎，在实际应用中表现优异
- **多元素识别**: 支持文字、表格、一维码、二维码等多种元素的准确识别
- **场景适配性强**: 特别适合标签打印场景，能够识别商品条码、价格标签等
- **技术稳定性**: 合合信息专注OCR领域多年，技术积累深厚

### 5.2 识别流程设计

```mermaid
graph TD
    A[用户上传图片] --> B[图片预处理]
    B --> C[调用合合信息OCR接口]
    C --> D[多元素识别结果]
    D --> E{识别内容分类}
    E --> F[文字内容处理]
    E --> G[表格结构处理]
    E --> H[条码/二维码处理]
    F --> I[智能排版处理]
    G --> I
    H --> I
    I --> J[生成模板预览]
    J --> K[用户确认/编辑]
    K --> L[保存为模板]
```

### 5.3 准确率优化策略

- **图片质量检测**: 模糊度、亮度检测，确保图片质量满足识别要求，不满足要求要做特殊处理
- **多元素智能识别**: 利用合合信息的全能识别能力，自动检测并处理文字、表格、条码
- **场景化优化**:
  - 商品标签: 重点优化条码、价格、商品名称识别
  - 表格数据: 利用表格识别能力，保持数据结构完整性
  - 混合内容: 智能区分文字、表格、条码区域，分别处理
- **后处理优化**:
  - 条码验证: 对识别的条码进行格式校验
  - 表格重构: 将识别的表格数据转换为标准化格式
  - 文字纠错: 基于上下文进行文字识别结果纠错

---

## 6. 团队协作功能方案

### 6.1 功能概述

团队协作为VIP付费功能，支持主账号创建子账号，实现团队成员间的模板共享、权限管理和操作审计。

### 6.2 账号体系设计

#### 主子账号关系

```
主账号 (Master Account)
├── 子账号1 (Sub Account 1)
├── 子账号2 (Sub Account 2)
└── 子账号N (Sub Account N)

权限层级:
主账号 > 子账号
```

#### 数据库设计

```javascript
// 账号关系表
const AccountRelationSchema = {
  id: 'string',
  master_account_id: 'string',  // 主账号ID
  sub_account_id: 'string',     // 子账号ID
  relation_type: 'master|sub',  // 关系类型
  permissions: {
    template_view: 'boolean',    // 查看模板权限
    template_edit: 'boolean',    // 编辑模板权限
    template_delete: 'boolean',  // 删除模板权限
    print_execute: 'boolean'     // 执行打印权限
  },
  status: 'active|suspended',   // 账号状态
  created_at: 'datetime',
  updated_at: 'datetime'
}

// 团队模板表
const TeamTemplateSchema = {
  id: 'string',
  template_id: 'string',        // 模板ID
  owner_id: 'string',           // 模板所有者
  team_id: 'string',            // 团队ID
  share_type: 'read|edit|full', // 共享类型
  shared_at: 'datetime',
  shared_by: 'string'           // 分享者ID
}
```

### 6.3 权限管理系统

#### 权限类型定义

```javascript
const TEAM_PERMISSIONS = {
  // 模板权限
  TEMPLATE_VIEW: 'template:view',
  TEMPLATE_CREATE: 'template:create',
  TEMPLATE_EDIT: 'template:edit',
  TEMPLATE_DELETE: 'template:delete',
  TEMPLATE_SHARE: 'template:share',

  // 打印权限
  PRINT_EXECUTE: 'print:execute',
  PRINT_HISTORY_VIEW: 'print:history:view',

  // 管理权限
  MEMBER_INVITE: 'member:invite',
  MEMBER_REMOVE: 'member:remove',
  PERMISSION_MANAGE: 'permission:manage'
}
```

#### 权限检查中间件

```javascript
function checkTeamPermission(permission) {
  return async (req, res, next) => {
    const { user_id, team_id } = req.user;
    const hasPermission = await TeamService.checkPermission(
      user_id,
      team_id,
      permission
    );

    if (!hasPermission) {
      return res.status(403).json({
        code: 403,
        message: '权限不足'
      });
    }

    next();
  };
}
```

### 6.4 模板共享机制

#### 共享类型

- **只读共享**: 子账号可查看模板，不能编辑
- **编辑共享**: 子账号可查看和编辑模板
- **完全共享**: 子账号拥有模板的所有权限

#### 共享流程

```mermaid
graph TD
    A[主账号选择模板] --> B[设置共享权限]
    B --> C[选择目标子账号]
    C --> D[发送共享邀请]
    D --> E[子账号接收通知]
    E --> F[子账号确认接受]
    F --> G[模板出现在子账号列表]
```

### 6.5 操作审计系统

#### 审计日志记录

```javascript
const AuditLogSchema = {
  id: 'string',
  team_id: 'string',           // 团队ID
  operator_id: 'string',       // 操作者ID
  operation_type: 'string',    // 操作类型
  target_type: 'string',       // 目标类型 (template|print|member)
  target_id: 'string',         // 目标ID
  operation_detail: 'object',  // 操作详情
  ip_address: 'string',        // 操作IP
  user_agent: 'string',        // 用户代理
  timestamp: 'datetime'
}
```

#### 可审计操作

- 模板创建/编辑/删除
- 模板共享/取消共享
- 打印任务执行
- 成员邀请/移除
- 权限变更

### 6.6 接口设计

```javascript
// 创建子账号
POST /api/v1/team/members
{
  "email": "<EMAIL>",
  "permissions": ["template:view", "print:execute"]
}

// 共享模板
POST /api/v1/team/templates/share
{
  "template_id": "string",
  "target_member_ids": ["member1", "member2"],
  "share_type": "read|edit|full"
}

// 获取团队操作日志
GET /api/v1/team/audit-logs?page=1&limit=20&operation_type=template

// 权限管理
PUT /api/v1/team/members/{member_id}/permissions
{
  "permissions": ["template:view", "template:edit"]
}
```

---

## 7. PC功能迁移方案

### 7.1 文本引用功能详细设计

#### 功能概述

文本引用为免费功能，支持在模板中引用其他元素的值，实现数据联动和复用。

#### 元素值引用机制

```javascript
// 可引用的元素类型
const REFERENCEABLE_ELEMENTS = {
  TEXT: 'text',           // 文本元素
  BARCODE: 'barcode',     // 一维码
  QRCODE: 'qrcode',       // 二维码
  DATETIME: 'datetime'    // 时间元素
}

// 引用数据结构
const ElementReference = {
  id: 'string',                    // 引用ID
  source_element_ids: ['string'],  // 被引用元素ID列表
  reference_type: 'single|multi',  // 引用类型：单个/多个
  join_separator: 'string',        // 多个元素连接符
  update_mode: 'auto|manual'       // 更新模式：自动/手动
}
```

#### 引用创建流程

```mermaid
graph TD
    A["用户新增文本组件"] --> B["选择数据类型为'元素值'"]
    B --> C["弹窗展示可引用元素"]
    C --> D["用户多选目标元素"]
    D --> E["设置拼接顺序"]
    E --> F["预览引用结果"]
    F --> G["确认创建引用"]
    G --> H["建立引用关系"]
```

#### 界面交互设计

- **选择界面**: 展示当前画板所有可引用元素，支持预览
- **多选支持**: 支持勾选多个元素，显示选择顺序
- **拼接预览**: 实时预览多个元素拼接后的效果
- **引用标识**: 被引用的元素显示特殊标识

### 7.2 批量打印功能详细设计

#### 功能概述

批量打印为免费功能，支持模板批量打印、多图打印、PDF批量打印等多种批量打印场景。

#### 模板批量打印

```javascript
// 批量打印任务结构
const BatchPrintTask = {
  id: 'string',
  user_id: 'string',
  task_type: 'template|image|pdf',
  items: [
    {
      template_id: 'string',
      copies: 'number',
      print_settings: {
        size: 'string',
        quality: 'string'
      }
    }
  ],
  total_items: 'number',
  completed_items: 'number',
  status: 'pending|printing|completed|failed',
  created_at: 'datetime'
}
```

#### 多图打印功能

- **图片选择**: 支持从相册多选图片
- **批量设置**: 统一设置打印尺寸、份数、质量
- **预览功能**: 批量预览打印效果
- **顺序调整**: 支持拖拽调整打印顺序

#### PDF批量打印功能

- **文件多选**: 支持选择多个PDF文件
- **页面选择**: 支持选择特定页面范围
- **合并打印**: 支持将多个PDF合并后打印
- **分页设置**: 支持设置每页打印参数

#### 移动端优化策略

- **后台处理**: 支持应用后台运行时继续打印
- **进度展示**: 实时显示打印进度和队列状态
- **异常恢复**: 网络中断、打印机离线等异常场景处理
- **暂停恢复**: 支持暂停和恢复批量打印任务
- **失败重试**: 自动重试失败的打印项目

#### 队列管理系统

```javascript
class BatchPrintQueue {
  constructor() {
    this.queue = [];
    this.isProcessing = false;
    this.maxConcurrent = 3; // 最大并发数
  }

  async addTask(task) {
    this.queue.push(task);
    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  async processQueue() {
    this.isProcessing = true;

    while (this.queue.length > 0) {
      const batch = this.queue.splice(0, this.maxConcurrent);
      await Promise.allSettled(
        batch.map(task => this.processPrintTask(task))
      );
    }

    this.isProcessing = false;
  }
}
```

### 7.3 共享打印功能设计

#### 功能概述

根据需求反馈，共享打印功能优先级降低，但仍需要基础的技术方案设计。

#### 实现方案

```mermaid
graph TD
    A[用户A连接打印机] --> B[开启共享打印]
    B --> C[生成共享码]
    C --> D[分享给用户B]
    D --> E[用户B输入共享码]
    E --> F[建立连接]
    F --> G[远程打印]
    G --> H{连接状态检查}
    H -->|连接正常| G
    H -->|连接断开| I[提示重新连接]
```

#### 技术挑战

- **网络稳定性**: 蓝牙/WiFi连接容易断开
- **权限控制**: 需要精细的权限管理机制
- **安全性**: 共享码的安全性和时效性
- **设备兼容**: 不同设备间的兼容性问题

#### 简化实现方案

```javascript
// 共享打印会话
const ShareSession = {
  id: 'string',
  share_code: 'string',      // 6位共享码
  host_user_id: 'string',    // 主机用户
  printer_id: 'string',      // 打印机ID
  expires_at: 'datetime',    // 过期时间
  max_uses: 'number',        // 最大使用次数
  status: 'active|expired|disabled'
}
```

#### 开发建议

- **当前版本**: 暂不实现，专注核心功能
- **V2.0版本**: 考虑基础共享功能
- **替代方案**: 通过团队协作功能部分满足共享需求

---

## 8. 技术架构总览

### 8.1 系统架构图

```
移动端APP
├── UI层 (React Native/Flutter)
├── 业务逻辑层
├── 数据访问层
└── 本地存储 (SQLite)
    ↓
API网关 (Kong/Nginx)
    ↓
微服务集群
├── 用户服务 (User Service)
├── 模板服务 (Template Service)
├── 打印服务 (Print Service)
├── OCR服务 (OCR Service)
├── 支付服务 (Payment Service)
└── 通知服务 (Notification Service)
    ↓
数据存储层
├── MySQL (用户数据、模板数据)
├── Redis (缓存、会话)
├── MongoDB (日志数据)
└── 对象存储 (图片、文件)
```

### 8.2 关键技术选型

- **移动端框架**: React Native (跨平台开发效率)
- **后端框架**: Node.js + Express (快速开发)
- **数据库**: MySQL 8.0 (主数据) + Redis 6.0 (缓存)
- **消息队列**: RabbitMQ (异步任务处理)
- **监控系统**: Prometheus + Grafana
- **部署方案**: Docker + Kubernetes

---

## 9. 开发时间节点与里程碑

### 9.1 开发阶段划分

```
Phase 1 (7.18-7.31): 基础架构搭建
├── VIP用户体系开发
├── 数据恢复基础功能
├── 打印日志功能
└── OCR服务集成

Phase 2 (8.1-8.15): 核心功能开发
├── 识图新建完整流程
├── 扫码新建功能
├── 文本引用功能
└── 批量打印队列系统

Phase 3 (8.16-8.31): 高级功能开发
├── 团队协作功能
├── 准确率优化 (目标>=90%)
├── 性能优化
└── 异常处理完善
```

### 9.2 技术风险评估

| 风险项          | 风险等级 | 影响       | 应对策略              |
| --------------- | -------- | ---------- | --------------------- |
| OCR准确率不达标 | 中       | 用户体验   | 合合信息多元素识别    |
| 数据同步延迟    | 中       | 功能可用性 | 本地缓存 + 重试机制   |
| 并发性能瓶颈    | 高       | 系统稳定性 | 负载均衡 + 缓存优化   |
| 第三方服务依赖  | 中       | 服务可用性 | 服务降级 + 熔断机制   |
| 团队协作复杂度  | 高       | 开发进度   | 分阶段实现 + 简化设计 |

---

## 10. 后续优化方向

### 10.1 V2.0规划

- AI智能排版算法优化
- 共享打印功能完善
- 多语言OCR支持
- 企业级功能扩展
- 高级团队协作功能

### 10.2 性能优化计划

- CDN加速图片处理
- 数据库读写分离
- 缓存策略优化
- 移动端离线能力增强
- 批量操作性能优化

---

## 附录

### A. 接口文档规范

- RESTful API设计原则
- 统一错误码定义
- 接口版本管理策略
- 团队协作接口规范

### B. 数据库设计文档

- 核心表结构设计
- 索引优化策略
- 数据迁移方案
- 团队数据隔离设计

### C. 安全设计方案

- 数据加密策略
- API安全防护
- 用户隐私保护
- 团队数据安全

---

**文档状态**: 需求更新完成，待技术评审
**更新内容**: 根据产品需求表新增打印日志、团队协作、扫码新建等功能方案
**下一步行动**: 技术评审会议，确认实现优先级和技术细节
